package dataQuery

import (
	"deskcrm/api/assistantdesk"
	"deskcrm/api/dataproxy"
	"deskcrm/api/examcore"
	"deskcrm/api/frontcourse"
	"deskcrm/api/jxexamui"
	"deskcrm/components/define"
	"fmt"
	"slices"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func (s *Singleton) GetInteractTotalNum(ctx *gin.Context, lessonIds []int64) (map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {

	detailRsp, err := dataproxy.NewClient().GetListByBindIdsBindTypeRelationTypesExamTags(ctx, dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindType:      RelationTypeLesson,
		BindIds:       lessonIds,
		RelationTypes: getTotalNumExamTypes,
		ExamTags:      []int64{0},
		Fields:        []string{"bindId", "relationType", "totalNum", "bindStatus"},
	})
	if err != nil {
		return nil, err
	}

	resMap := make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range detailRsp {
		temp := item

		resMap[temp.BindId] = temp
	}

	return resMap, nil
}

func (s *Singleton) GetExamBindData(ctx *gin.Context, lessonId int64, bindType int) (assistantdesk.ExamBindRes, error) {

	detailRsp, err := assistantdesk.NewClient().GetExamBindStatus(ctx, assistantdesk.GetExamBindStatusParams{
		LessonId: lessonId,
		BindType: bindType,
	})
	if err != nil {
		return detailRsp, err
	}

	return detailRsp, nil
}

// GetILabInfo 获取iLab课程信息
// 对应PHP中的initILabInfo方法
func (s *Singleton) GetILabInfo(ctx *gin.Context, gradeId, subjectId int, lessonIds []int64, studentUid int64) (*ILabInfo, error) {
	result := &ILabInfo{
		PreviewInfoByIlab:     make(map[int64]int),
		HomeworkInfoByIlab:    make(map[int64]int),
		InclassTestInfoByIlab: make(map[int64]int),
		HomeworkInfoByNomal:   make(map[int64]int),
		CheckIlabLesson:       make(map[int64]jxexamui.ExamTypeInfo),
	}

	// 只处理初二物理课程(gradeId=3, subjectId=4)
	acceptGrade := []int{3, 4}
	acceptSubject := []int{4} // 物理学科ID

	gradeMatch := slices.Contains(acceptGrade, gradeId)
	subjectMatch := slices.Contains(acceptSubject, subjectId)
	if !gradeMatch || !subjectMatch {
		return result, nil
	}

	// 获取章节试卷类型信息
	checkIlabLesson, err := jxexamui.NewClient().GetExamTypeByLessonIds(ctx, lessonIds)
	if err != nil {
		zlog.Warnf(ctx, "GetILabInfo GetExamTypeByLessonIds failed: %v", err)
		return result, nil
	}
	result.CheckIlabLesson = checkIlabLesson

	if len(checkIlabLesson) == 0 {
		return result, nil
	}

	// 获取学生各类型试卷的结果等级
	examTypes := []int{
		jxexamui.BindTypeHomeworkIlab, // iLab巩固练习
		jxexamui.BindTypePosttestMore, // 初高中预习测试
		jxexamui.BindTypeTestInClass,  // 堂堂测
		jxexamui.BindTypeHomework,     // 普通巩固练习
	}

	ilabRet, err := jxexamui.NewClient().GetLevelByExamTypeUidLessonIds(ctx, studentUid, examTypes, lessonIds)
	if err != nil {
		zlog.Warnf(ctx, "GetILabInfo GetLevelByExamTypeUidLessonIds failed: %v", err)
		return result, nil
	}

	// 填充结果
	if levels, ok := ilabRet[jxexamui.BindTypeHomeworkIlab]; ok {
		result.HomeworkInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypePosttestMore]; ok {
		result.PreviewInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypeTestInClass]; ok {
		result.InclassTestInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypeHomework]; ok {
		result.HomeworkInfoByNomal = levels
	}

	return result, nil
}

// GetPreviewOpenInfo 获取预习开启状态信息
// 对应PHP中的initPreviewIsOpenInfos方法
func (s *Singleton) GetPreviewOpenInfo(ctx *gin.Context, courseId int64, lessonIds []int64, lessonList map[int64]interface{}, gradeStage int) (map[int64]PreviewOpenInfo, error) {
	result := make(map[int64]PreviewOpenInfo)

	if len(lessonIds) == 0 {
		return result, nil
	}

	now := time.Now().Unix()

	switch gradeStage {
	case define.GradeStagePrimary: // 小学和低幼学部
		// 小学预习情况 - 通过试卷绑定关系判断
		bindList := make([]string, 0, len(lessonIds))
		for _, lessonId := range lessonIds {
			bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, jxexamui.BindTypePreview)
			bindList = append(bindList, bindKey)
		}

		examRelationList, err := examcore.NewClient().GetRelation(ctx, bindList)
		if err != nil {
			zlog.Warnf(ctx, "GetPreviewOpenInfo GetRelation failed: %v", err)
			examRelationList = make(map[string]map[int64]examcore.BindInfo)
		}

		for _, lessonId := range lessonIds {
			bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, jxexamui.BindTypePreview)
			examRelationDetail := examRelationList[bindKey]

			hasPreview := 0
			if len(examRelationDetail) > 0 {
				hasPreview = 1
			}

			isOpenPreview := 0
			if hasPreview == 1 {
				// 获取章节开始时间，判断是否在开课7天内
				if lessonInfo, ok := lessonList[lessonId]; ok {
					if lessonMap, ok := lessonInfo.(map[string]interface{}); ok {
						if startTimeInterface, ok := lessonMap["startTime"]; ok {
							if startTime, ok := startTimeInterface.(int64); ok {
								// 开课7天前开启预习
								sevenDaysBeforeStart := startTime - (7 * 24 * 3600)
								if now > sevenDaysBeforeStart {
									isOpenPreview = 1
								}
							}
						}
					}
				}
			}

			result[lessonId] = PreviewOpenInfo{
				HasPreview:    hasPreview,
				IsOpenPreview: isOpenPreview,
			}
		}

	case define.GradeStageJunior, define.GradeStageSenior: // 初高中
		// 初高中预习情况 - 通过frontcourse接口获取
		apiData, err := frontcourse.NewClient().GetHighGradePreviewInfo(ctx, lessonIds)
		if err != nil {
			zlog.Warnf(ctx, "GetPreviewOpenInfo GetHighGradePreviewInfo failed: %v", err)
			apiData = make(map[int64]frontcourse.HighGradePreviewInfo)
		}

		for _, lessonId := range lessonIds {
			hasPreview := 0
			isOpenPreview := 0

			if info, ok := apiData[lessonId]; ok {
				if info.Status > 0 {
					hasPreview = 1
				}
				isOpenPreview = info.IsOpen
			}

			result[lessonId] = PreviewOpenInfo{
				HasPreview:    hasPreview,
				IsOpenPreview: isOpenPreview,
			}
		}

	default:
		// 其他学段暂不处理
		for _, lessonId := range lessonIds {
			result[lessonId] = PreviewOpenInfo{
				HasPreview:    0,
				IsOpenPreview: 0,
			}
		}
	}

	return result, nil
}
