package lessonDataFunc

import (
	"deskcrm/api/achilles"
	"deskcrm/api/dal"
	"deskcrm/api/das"
	"deskcrm/api/dataproxy"
	"deskcrm/api/jxexamui"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// GetLessonId 获取章节ID
// 对应PHP中的lessonId字段，直接返回lessonID
func (s *Format) GetLessonId(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}

	for _, lessonID := range s.param.LessonIDs {
		// 直接返回lessonID，对应PHP中的 $row['lessonId'] = $val['lessonId']
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonID)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节ID】", "直接返回lessonID参数")
	return
}

func (s *Format) GetLessonName(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonName := ""
		if _, ok := lessonMap[lessonID]; ok {
			lessonName = lessonMap[lessonID].LessonName
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonName)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节名称】 ", "dal courseinfo")
	return
}

// GetType 获取章节类型
// 对应PHP中的type字段，来源于lessonType
func (s *Format) GetType(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonType := 0
		if _, ok := lessonMap[lessonID]; ok {
			lessonType = lessonMap[lessonID].LessonType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节类型】", "dal courseinfo")
	return
}

// GetPlayType 获取播放类型
// 对应PHP中的playType字段
func (s *Format) GetPlayType(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playType := 0
		if _, ok := lessonMap[lessonID]; ok {
			playType = lessonMap[lessonID].PlayType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取播放类型】", "dal courseinfo")
	return
}

// GetInclassTime 获取上课时间
// 对应PHP中的inclassTime字段，格式化为"YYYY-MM-DD HH:MM-HH:MM"
func (s *Format) GetInclassTime(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		inclassTime := ""
		if _, ok := lessonMap[lessonID]; ok {
			startTime := lessonMap[lessonID].StartTime
			stopTime := lessonMap[lessonID].StopTime
			// 格式化为 "2006-01-02 15:04-15:04" 格式，对应PHP的LESSON_INCLASS_TIME
			startTimeStr := time.Unix(int64(startTime), 0).Format("2006-01-02 15:04")
			stopTimeStr := time.Unix(int64(stopTime), 0).Format("15:04")
			inclassTime = startTimeStr + "-" + stopTimeStr
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取上课时间】", "dal courseinfo")
	return
}

// GetStopTime 获取章节结束时间
// 对应PHP中的stopTime字段
func (s *Format) GetStopTime(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		stopTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			stopTime = lessonMap[lessonID].StopTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, stopTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节结束时间】", "dal courseinfo")
	return
}

// GetStartTime 获取章节开始时间
// 对应PHP中的startTime字段
func (s *Format) GetStartTime(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		startTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			startTime = lessonMap[lessonID].StartTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, startTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节开始时间】", "dal courseinfo")
	return
}

// GetPreview 获取预习数据
// 对应PHP中的preview字段
func (s *Format) GetPreview(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{}) {
		return
	}

	// 获取基础LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"previewTotalNum", "previewCorrectNum", "previewParticipateNum", "isPreviewFinish"},
	})
	if err != nil {
		return
	}

	luData := queryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)

	// 获取课程信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)
	gradeId := int(courseData.MainGradeId)
	subjectId := int(courseData.MainSubjectId)

	// 获取学段信息
	gradeStage := define.Grade2XB[gradeId]

	// 获取iLab信息（仅针对初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{
		gradeId, subjectId, s.param.LessonIDs, s.param.StudentUid,
	})
	if err != nil {
		return
	}
	ilabData := ilabInfo.(*dataQuery.ILabInfo)

	// 获取预习开启状态信息
	previewOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetPreviewOpenInfo", []interface{}{
		s.param.CourseID, s.param.LessonIDs, nil, gradeStage,
	})
	if err != nil {
		return
	}
	previewOpenData := previewOpenInfo.(map[int64]dataQuery.PreviewOpenInfo)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化预习数据数组：[显示文本, 颜色, 是否可点击]
		previewArray := NewLessonDataArray("-", "gray", 1)

		if lessonLuData, ok := luData[lessonID]; ok {
			correctNum := lessonLuData.PreviewCorrectNum
			participateNum := lessonLuData.PreviewParticipateNum
			totalNum := lessonLuData.PreviewTotalNum

			// 基础预习数据格式化
			if totalNum == 0 {
				previewArray[0] = "-"
			} else {
				previewArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, totalNum)
			}

			// iLab兼容逻辑 - 针对初二物理课程
			if gradeId == 3 && subjectId == 4 {
				if checkInfo, exists := ilabData.CheckIlabLesson[lessonID]; exists && checkInfo.ILabLesson {
					if level, hasLevel := ilabData.PreviewInfoByIlab[lessonID]; hasLevel {
						if levelText, ok := jxexamui.LevelIlabMap[level]; ok && levelText != "" {
							previewArray[0] = levelText
							// 根据iLab等级设置颜色
							switch level {
							case 1:
								previewArray[1] = "green" // 优秀
							case 2:
								previewArray[1] = "orange" // 良好
							}
						} else {
							previewArray[0] = "-"
						}
					}
				}
			}

			// 预习开启状态检查
			if previewArray[0] == "-" {
				if openInfo, exists := previewOpenData[lessonID]; exists {
					if openInfo.IsOpenPreview == 1 {
						// 检查预习完成状态
						if lessonLuData.IsPreviewFinish == 1 {
							previewArray[0] = "0/0/0"
						} else {
							previewArray[0] = "未提交"
						}
					} else {
						// 预习未开启，显示"-"
						previewArray[0] = "-"
					}
				}
				previewArray[2] = 0
			}
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, previewArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取预习数据】", "LU: previewCorrectNum, previewParticipateNum, previewTotalNum, isPreviewFinish; iLab兼容; 预习开启状态检查")
	return
}

// GetAttendData 获取到课数据
// 对应PHP中的attend字段
func (s *Format) GetAttendData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据 - attendDuration字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"attendDuration"},
	})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)

	// 获取学生章节请假信息
	lessonStudentData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	studentLeaveData := lessonStudentData.(map[int64]*models.LessonStudent)

	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := lessonInfoData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		// 获取attendDuration
		attendDuration := int64(0)
		if lessonData, ok := luData[lessonID]; ok {
			attendDuration = lessonData.AttendDuration
		}

		// 获取playType
		playType := int64(0)
		if lesson, ok := lessonMap[lessonID]; ok {
			playType = int64(lesson.PlayType)
		}

		attend := FormatDuration(attendDuration)
		if playType == dal.PLAY_TYPE_LUBOKE {
			attend = "-"
			attendDuration = 0
		}

		attendCode := 0
		leaveSeason := ""
		if studentInfo, ok := studentLeaveData[lessonID]; ok {
			if studentInfo.PreAttend == models.PreAttendLeave {
				attendCode = 3
				if studentInfo.ExtData.LeaveSeason != "" {
					leaveSeason = studentInfo.ExtData.LeaveSeason
				}
			}
		} else {
			attendCode = 0
		}

		// 添加到课数据
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attend)
		// 添加到课状态码和请假原因
		_ = s.AddOutputStudent(ctx, lessonID, "attendCode", attendCode)
		_ = s.AddOutputStudent(ctx, lessonID, "leaveSeason", leaveSeason)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【直播到课时长】", "ES: attendDuration, DAL: playType, DB: tblLessonStudent学生请假信息")
	return
}

// GetPlayback 获取回放数据
// 对应PHP中的playback字段，格式化为"XminYs"或"-"（录播课程）
func (s *Format) GetPlayback(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取LU数据 - 回放时长相关字段
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"playbackTotalTime", "playbackTimeAfterUnlock"},
	})
	if err != nil {
		return
	}

	commonluData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{
		s.param.LessonIDs,
		s.param.StudentUid,
		[]string{"inclass_teacher_room_total_playback_time_v1"},
	})
	if err != nil {
		return
	}

	// 获取章节基础信息 - t007Tag和playType字段
	lessonBaseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonBaseInfo", []interface{}{
		s.param.LessonIDs,
		[]string{"t007Tag", "playType"},
	})
	if err != nil {
		return
	}

	luDataMap := luData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)
	commonluDataMap := commonluData.(map[int64]*dataproxy.GetCommonLuResp)
	lessonInfoMap := lessonBaseInfo.(map[int64]*achilles.ProcessedLessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playback := "-"
		playbackV1 := ""

		if lessonLuData, ok := luDataMap[lessonID]; ok {
			var playbackTotalTime int64

			// 根据t007Tag决定使用哪个回放时长
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.T007Tag == 1 {
				playbackTotalTime = lessonLuData.PlaybackTimeAfterUnlock
			} else {
				playbackTotalTime = lessonLuData.PlaybackTotalTime
			}
			// 检查是否为录播课程
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.PlayType == dal.PLAY_TYPE_LUBOKE {
				playback = "-"
			} else {
				// 格式化回放时长
				playback = FormatDuration(playbackTotalTime)
			}
		}

		if commonLessonData, ok := commonluDataMap[lessonID]; ok {
			playbackV1 = FormatDuration(commonLessonData.InclassTeacherRoomTotalPlaybackTimeV1)
		}

		// 输出回放时长
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playback)
		_ = s.AddOutputStudent(ctx, lessonID, "playbackV1", playbackV1)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【回放时长】", "LU: playbackTotalTime, playbackTimeAfterUnlock + Achilles: t007Tag, playType")
	return
}

// GetPlaybackOnlineTimeV1 获取回放观看时长(新)
// 对应PHP中的playbackv1字段，格式化为"XminYs"
func (s *Format) GetPlaybackOnlineTimeV1(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"inclass_teacher_room_total_playback_time_v1"},
	})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)

	for _, lessonID := range s.param.LessonIDs {
		playbackV1Data := ""

		if lessonData, ok := studentLessonData[lessonID]; ok {
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			playbackV1Data = FormatDuration(lessonData.InclassTeacherRoomTotalPlaybackTimeV1)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playbackV1Data)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取回放观看时长(新)】", "ES: inclass_teacher_room_total_playback_time_v1")
	return
}

// GetLbpAttendDuration 获取LBP观看时长
// 对应PHP中的lbpAttendDuration字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDuration(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{
		s.param.LessonIDs,
		s.param.StudentUid,
		[]string{"inclass_teacher_room_total_playback_content_time"},
	})
	if err != nil {
		return
	}

	commonLuData := queryData.(map[int64]*dataproxy.GetCommonLuResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDuration := ""
		if lessonData, ok := commonLuData[lessonID]; ok {
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			lbpAttendDuration = FormatDuration(lessonData.InclassTeacherToomTotalPlaybackContentTime)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDuration)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长】", "CommonLU: inclass_teacher_room_total_playback_content_time")
	return
}

// GetLbpAttendDurationOld 获取LBP观看时长(旧版)
// 对应PHP中的lbpAttendDurationOld字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDurationOld(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"lbp_attend_duration"},
	})
	if err != nil {
		return
	}

	studentLessonData := queryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDurationOld := ""

		if lessonData, ok := studentLessonData[lessonID]; ok {
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			lbpAttendDurationOld = FormatDuration(lessonData.LbpAttendDuration)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDurationOld)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长(旧版)】", "ES: lbp_attend_duration")
	return
}

// GetOralQuestion 获取口述题数据
// 对应PHP中的oralQuestion字段，包含复杂的状态判断逻辑
func (s *Format) GetOralQuestion(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 口述题状态常量
	const (
		ORALQU_STATUS_UNKNOWN      = -1
		ORALQU_STATUS_UNSUBMIT     = 0
		ORALQU_STATUS_SUBMIT       = 1
		ORALQU_STATUS_TB_CORRECTED = 2
	)

	// 状态映射
	statusMap := map[int]string{
		ORALQU_STATUS_UNKNOWN:      "-",
		ORALQU_STATUS_UNSUBMIT:     "未提交",
		ORALQU_STATUS_SUBMIT:       "已完成",
		ORALQU_STATUS_TB_CORRECTED: "待批改",
	}

	// 获取LU数据 - 口述题相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{"oralQuestionSubmit", "oralQuestionCorrectTime"},
	})
	if err != nil {
		return
	}

	// 获取试卷绑定数据 - 口述题绑定信息
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.BindTypeOralQuestion,
		[]int{dataQuery.RelationTypeLesson},
		[]string{"bind_id", "relation_type", "bind_status", "is_artificial_correct"},
	})
	if err != nil {
		return
	}

	luData := luQueryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)
	examRelationData := examData.(map[string]interface{})

	for _, lessonID := range s.param.LessonIDs {
		// 初始化口述题数据数组：[显示文本, 颜色, 是否可点击]
		oralQuestionArray := NewLessonDataArray("-", "gray", 0)
		status := ORALQU_STATUS_UNKNOWN

		// 检查是否有口述题绑定
		lessonBindId := fmt.Sprintf("%d", lessonID)
		if examInfo, exists := examRelationData[lessonBindId]; exists {
			if examMap, ok := examInfo.(map[string]interface{}); ok {
				bindStatus := false
				isArtificialCorrect := false

				if bindStatusVal, exists := examMap["bind_status"]; exists {
					if val, ok := bindStatusVal.(int64); ok && val > 0 {
						bindStatus = true
					}
				}

				if artificialVal, exists := examMap["is_artificial_correct"]; exists {
					if val, ok := artificialVal.(int64); ok && val > 0 {
						isArtificialCorrect = true
					}
				}

				if bindStatus {
					// 获取学生口述题数据
					if lessonLuData, exists := luData[lessonID]; exists {
						oralQuestionSubmit := lessonLuData.OralQuestionSubmit > 0
						oralQuestionCorrectTime := lessonLuData.OralQuestionCorrectTime

						if oralQuestionSubmit {
							status = ORALQU_STATUS_SUBMIT
							// 需要批改且没有批改（根据时间判断），则展示待批改
							if isArtificialCorrect && oralQuestionCorrectTime <= 0 {
								status = ORALQU_STATUS_TB_CORRECTED
							}
						} else {
							status = ORALQU_STATUS_UNSUBMIT
						}
					} else {
						status = ORALQU_STATUS_UNSUBMIT
					}
				}
			}
		}

		// 设置显示文本
		oralQuestionArray[0] = statusMap[status]

		// 设置颜色和可点击状态
		if status == ORALQU_STATUS_SUBMIT || status == ORALQU_STATUS_TB_CORRECTED {
			oralQuestionArray[1] = "green"
			oralQuestionArray[2] = 1 // 可点击
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, oralQuestionArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取口述题数据】", "LU: oralQuestionSubmit, oralQuestionCorrectTime + Exam: bind_status, is_artificial_correct")
	return
}

// GetInclassTest 获取堂堂测数据
// 对应PHP中的inclassTest字段，格式化为"X/Y/Z"（正确数/参与数/总数）或小英课程得分
func (s *Format) GetInclassTest(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}

	// 获取LU数据 - 堂堂测相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		[]int64{s.param.CourseID},
		[]int64{s.param.StudentUid},
		[]string{
			"tangTangExamCorrectNum",
			"tangTangExamParticipateNum",
			"tangTangExamScore",
			"isTangTangExamSubmit",
		},
	})
	if err != nil {
		return
	}

	// 获取考试绑定数据 - 堂堂测总数
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.BindTypeTestInClass,
		[]int{dataQuery.RelationTypeLesson},
		[]string{"bind_id", "relation_type", "total_num"},
	})
	if err != nil {
		return
	}

	luData := luQueryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)
	examRelationData := examData.(map[string]interface{})

	// 检查是否是小英课程
	isHxCourse := false
	// TODO: 需要实现checkIsHx方法，这里暂时使用默认值
	// isHxCourse = checkIsHx(s.param.CourseID)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化堂堂测数据数组：[显示文本, 颜色, 是否可点击]
		inclassTestArray := NewLessonDataArray("-", "gray", 1)

		// 获取堂堂测总数
		tangTangTotalNum := int64(0)
		if examInfo, exists := examRelationData[fmt.Sprintf("%d", lessonID)]; exists {
			if examMap, ok := examInfo.(map[string]interface{}); ok {
				if totalNum, exists := examMap["total_num"]; exists {
					if totalVal, ok := totalNum.(int64); ok && totalVal > 0 {
						tangTangTotalNum = totalVal
					}
				}
			}
		}

		// 获取学生堂堂测数据
		if lessonLuData, exists := luData[lessonID]; exists {
			correctNum := lessonLuData.TangTangExamCorrectNum
			participateNum := lessonLuData.TangTangExamParticipateNum
			score := lessonLuData.TangTangExamScore
			isSubmit := lessonLuData.IsTangTangExamSubmit > 0

			// 根据课程类型显示不同格式
			if !isHxCourse {
				// 非小英课程：显示 "X|Y|Z" 格式，对应PHP的sprintf(self::LESSON_EXERCISE_DETAIL, $tangTangExamCorrectNum, $tangTangExamParticipateNum, $tangTangToalNum)
				if tangTangTotalNum > 0 {
					inclassTestArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, tangTangTotalNum)
					// 设置颜色：满分绿色，不满分橙色
					if correctNum == tangTangTotalNum {
						inclassTestArray[1] = "green" // 满分
					} else if correctNum < tangTangTotalNum {
						inclassTestArray[1] = "orange" // 不满分
					}
				} else {
					inclassTestArray[0] = "-"
				}
			} else {
				// 小英课程：展示得分，对应PHP的sprintf('%s分', ($esLessonData[self::$studentUid]['tangTangExamScore'] / 10))
				if isSubmit {
					inclassTestArray[0] = fmt.Sprintf("%d分", score/10)
				} else {
					inclassTestArray[0] = "未提交"
				}
			}
		} else {
			if !isHxCourse {
				inclassTestArray[0] = "-"
			} else {
				inclassTestArray[0] = "未提交"
			}
		}

		// 如果显示为"-"，设置不可点击
		if inclassTestArray[0] == "-" {
			inclassTestArray[2] = 0
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTestArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取堂堂测数据】", "LU: tangTangExamCorrectNum, tangTangExamParticipateNum, tangTangExamScore, isTangTangExamSubmit + Exam: total_num")
	return
}

// GetHomeworkData 获取作业数据
// 对应PHP中的homework字段，格式化为["状态", "颜色", "是否可点击"]
func (s *Format) GetHomeworkData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{}) {
		return
	}

	// 获取LU数据 - 作业等级等
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{
		s.param.CourseID,
		s.param.StudentUid,
		[]string{"homeworkLevel", "homeworkPracticeCorrectNum", "homeworkPracticeParticipateNum"},
	})
	if err != nil {
		return err
	}

	// 获取DAS数据 - 作业状态和订正状态
	dasQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonData", []interface{}{
		[]int64{s.param.StudentUid}, s.param.LessonIDs,
	})
	if err != nil {
		return err
	}

	// 获取作业绑定数据
	hwBindExams, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkBindExams", []interface{}{
		s.param.LessonIDs,
		7, // 作业绑定类型，对应PHP中的BindTypeHomework
	})
	if err != nil {
		return err
	}

	// 获取课程信息 - 用于获取年级和学科信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{
		s.param.CourseID,
	})
	if err != nil {
		return err
	}

	// 获取作业开启信息 - 暂时跳过，需要课程信息
	// TODO: 需要从其他地方获取课程信息
	homeworkOpenInfo := make(map[int64]jxexamui.HomeworkOpenInfo)

	// 获取ILab信息（如果是初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{
		courseInfo.(dal.CourseInfo).MainGradeId,
		courseInfo.(dal.CourseInfo).MainSubjectId,
		s.param.LessonIDs,
		s.param.StudentUid,
	})
	if err != nil {
		return err
	}

	// 获取需要审核的课程映射
	lessonNeedAuditMap, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonNeedAuditMap", []interface{}{
		s.param.LessonIDs,
	})
	if err != nil {
		return err
	}

	// 类型转换
	luData := luQueryData.(map[int64]*dataproxy.GetListByCourseIdsStudentUidsResp)
	dasData := dasQueryData.(map[int64]map[int64]*das.StudentLessonInfo)
	bindExams := hwBindExams.(map[int64]bool)
	openInfo := homeworkOpenInfo
	ilab := ilabInfo.(*dataQuery.ILabInfo)
	course := courseInfo.(dal.CourseInfo)
	auditMap := lessonNeedAuditMap.(map[int64]bool)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化作业数据数组：[显示文本, 颜色, 是否可点击]
		homeworkArray := NewLessonDataArray("-", "gray", 1)

		// 获取当前章节的数据
		lessonLuData := luData[lessonID]
		dasStudentLessonInfo := dasData[s.param.StudentUid][lessonID]
		isBindHw := bindExams[lessonID]
		homeworkOpenStatus := openInfo[lessonID]

		// 兼容das作业未布置错误问题
		if dasStudentLessonInfo != nil && dasStudentLessonInfo.HomeworkStatus == 0 && isBindHw {
			dasStudentLessonInfo.HomeworkStatus = 1
		}

		// 判断是否为订正课程 - 暂时设为false，后续需要从其他地方获取
		isAmendCourse := false

		s.processHomeworkLogic(ctx, lessonID, homeworkArray, lessonLuData, dasStudentLessonInfo,
			isBindHw, homeworkOpenStatus, ilab, isAmendCourse, course, auditMap)

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取作业数据】", "DAS: homeworkStatus + LU: homeworkLevel + 作业绑定数据 + 开启状态 + ILab")
	return
}

// processHomeworkLogic 处理作业逻辑
// 对应PHP中getHomeworkData函数的核心逻辑
func (s *Format) processHomeworkLogic(ctx *gin.Context, lessonID int64, homeworkArray []interface{},
	lessonLuData *dataproxy.GetListByCourseIdsStudentUidsResp,
	dasStudentLessonInfo *das.StudentLessonInfo,
	isBindHw bool, homeworkOpenStatus jxexamui.HomeworkOpenInfo,
	ilab *dataQuery.ILabInfo, isAmendCourse bool, course dal.CourseInfo, auditMap map[int64]bool) {

	// 使用consts包中的作业等级映射
	homeworkLevelMap := consts.HomeworkLevelMap

	// 处理订正课程逻辑
	if isAmendCourse {
		homeworkArray[0] = "-"
		homeworkArray[1] = "gray"
		homeworkArray[2] = 1

		// 检查作业是否开启
		if homeworkOpenStatus.IsOpen != consts.HomeworkOpen {
			homeworkArray[0] = "-"
		} else if !isBindHw {
			homeworkArray[0] = "未布置"
		} else if dasStudentLessonInfo != nil && dasStudentLessonInfo.HomeworkLevel > 0 {
			// 有作业等级，显示等级
			if levelText, exists := homeworkLevelMap[int64(dasStudentLessonInfo.HomeworkLevel)]; exists {
				homeworkArray[0] = levelText
			} else {
				homeworkArray[0] = "暂无等级"
			}
		} else if dasStudentLessonInfo != nil {
			// 处理订正状态
			switch dasStudentLessonInfo.HomeworkRecorrect {
			case 2:
				homeworkArray[0] = "待批改"
				// 检查是否需要审核
				if auditMap[lessonID] {
					homeworkArray[0] = "待审核"
				}
			case 4:
				homeworkArray[0] = "待重批"
			case 5:
				homeworkArray[0] = "待重提"
			default:
				homeworkArray[0] = "未提交"
			}
		} else {
			homeworkArray[0] = "未提交"
		}
	} else {
		// 处理普通课程逻辑
		homeworkArray[0] = "-"
		homeworkArray[1] = "gray"
		homeworkArray[2] = 1

		// 检查作业是否开启
		if homeworkOpenStatus.IsOpen != consts.HomeworkOpen {
			homeworkArray[0] = "-"
		} else if !isBindHw {
			homeworkArray[0] = "未布置"
		} else if dasStudentLessonInfo != nil {
			switch dasStudentLessonInfo.HomeworkStatus {
			case 1:
				homeworkArray[0] = "未提交"
			case 2:
				homeworkArray[0] = "未批改"
			case 3:
				// 已批改，显示等级
				if dasStudentLessonInfo.HomeworkLevel > 0 {
					if levelText, exists := homeworkLevelMap[int64(dasStudentLessonInfo.HomeworkLevel)]; exists {
						homeworkArray[0] = levelText
					} else {
						homeworkArray[0] = "暂无等级"
					}
				} else {
					homeworkArray[0] = "暂无等级"
				}
			default:
				homeworkArray[0] = "-"
			}
		}
	}

	// 设置颜色
	if homeworkArray[0] == HOMEWORK_FULL_MARKS_CODE {
		homeworkArray[1] = "green" // 满分
	} else {
		// 检查是否为其他等级（不满分）
		for _, levelText := range homeworkLevelMap {
			if homeworkArray[0] == levelText && homeworkArray[0] != HOMEWORK_FULL_MARKS_CODE {
				homeworkArray[1] = "orange" // 不满分
				break
			}
		}
	}

	// 处理ILab兼容逻辑（初二物理课程）
	// 初二物理：gradeId=3, subjectId=4
	if course.MainGradeId == 3 && course.MainSubjectId == 4 && ilab != nil {
		// 检查是否为ILab v2章节
		if ilabLessonInfo, exists := ilab.CheckIlabLesson[lessonID]; exists && ilabLessonInfo.Version == 2 {
			// ILab v2 逻辑：使用ILab的作业信息
			if ilabHomeworkStatus, exists := ilab.HomeworkInfoByIlab[lessonID]; exists && ilabHomeworkStatus > 0 {
				// 有ILab作业状态，根据状态设置显示文本
				switch ilabHomeworkStatus {
				case 1:
					homeworkArray[0] = "未提交"
				case 2:
					homeworkArray[0] = "未批改"
				case 3:
					// 已批改，但ILab的等级信息需要从其他地方获取
					// 暂时显示已批改状态
					homeworkArray[0] = "已批改"
				default:
					homeworkArray[0] = "-"
				}
			}
		}
	}

	// 设置不可点击状态
	if homeworkArray[0] == "-" {
		homeworkArray[2] = 0
	}
}
